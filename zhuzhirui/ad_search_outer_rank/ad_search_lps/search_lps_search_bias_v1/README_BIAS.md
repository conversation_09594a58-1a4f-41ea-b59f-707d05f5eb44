## 实现概述

本实现为搜索LPS模型添加了bias建模功能，通过并行的bias网络来利用完整特征信息（包括被mask的搜索特征），提升模型在搜索场景下的效果。

**核心思想**: 主网络使用mask后的特征训练通用能力，bias网络使用完整特征（包括搜索特征）学习搜索场景的bias，两者logits相加得到最终预测。


```
完整特征(含搜索) ──┐
                ├─→ Bias Share Bottom ─→ Bias CTCVR Head ─→ bias_logits
                │                        (含leave_seq_esmm)      │
                │                                                ▼
Mask特征(无搜索) ─→ Share Bottom ─→ CTCVR Head ─→ main_logits ─→ 融合 ─→ 最终输出
                                  (无leave_seq_esmm)
```
