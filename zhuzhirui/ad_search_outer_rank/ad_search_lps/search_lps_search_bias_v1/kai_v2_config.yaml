backend_config:
  allow_growth: true
  disable_sparse_scatter: false
  disable_sparse_segment_sum: false
  enable_ncu: false
  enable_recompute: false
  enable_timeline: false
  gpu_memory_fraction: 0.75
  inter_op_parallelism_threads: 0
  intra_op_parallelism_threads: 0
  log_device_placement: false
  ncu_path: ''
  recompute_strategy: 0
  timeline_begin_step: 10
  timeline_end_step: 10
  timeline_file_dir: ./
  use_per_session_threads: true
backend_config_trigger: TensorflowBackendConfig
btq_save_option:
  btq_base_thresh: 0.0
  btq_batch_feature_expire_seconds: 108000
  btq_channel_block_size: 2048
  btq_channel_buffer_size: 1073741824
  btq_channel_capacity: 5000000
  btq_delta_thresh: 10.0
  btq_emb_compress_type: mio_int16
  btq_prefix: ''
  btq_realtime_feature_expire_seconds: 14400
  btq_sender_thread_num_per_topic: 4
  btq_topic_by_slot: {}
  dnn_topic: ''
  send_base_at_new_day: true
  send_base_at_train_end: false
  send_base_hours: ''
  send_base_interval_hour: 1
  send_base_interval_pass: -1
  send_delta_interval_pass: -1
  send_delta_interval_time_ms: 3600000
  use_btq_save: false
btq_save_option_trigger: SlotBtqSaveOption
distributed_strategy:
  debug_config.enable_dnn_weight_delta_perf_log: false
  debug_config.enable_dnn_weight_delta_perf_report: false
  dense_shard_num: 32
  dtype.calculate: 3
  dtype.storage: 3
  dtype.transfer.dense_pull: 3
  dtype.transfer.dense_push: 3
  dtype.transfer.inference_pull: 3
  dtype.transfer.sparse_pull: 3
  dtype.transfer.sparse_push: 3
  grad_no_scale: false
  interleaving_enabled: false
  interleaving_option.pipeline_num: 4
  parallel_per_device: 1
  remote_emb.batch_size: 1024
  remote_emb.retry: 3
  remote_emb.service.client_side_shard: false
  remote_emb.service.cluster: PRODUCTION
  remote_emb.service.name: ''
  remote_emb.service.shard_num: 1
  remote_emb.service.shard_prefix: s
  remote_emb.service.timeout_ms: 10
  remote_emb.slots: []
  shard_num: 32
  sparse_grad_merge_mode: 0
  sparse_table_config.create_prob_mul_show: false
  sparse_table_config.estimate_load_factor: 0.1
  sparse_table_config.max_key_nums: 1000000000
  sparse_table_config.recycle_stat_accuracy: 100
  sparse_table_config.recycle_stat_bucket_size: 1024
  sparse_table_config.sparse_lookup_last: false
  use_avx: false
  use_dense_cache: true
distributed_strategy_trigger: KaiSyncDistributedStrategy
io_config:
  train:
    batch_capacity: 8
    batch_size: 1024
    begin_time_ms: 0
    cache_batch_num: 0
    channel_capacity: 0
    choose_max_from_reader_and_load: true
    click_score_coeff: 0.0
    dedup_showcount_in_sample: false
    end_time_ms: 0
    force_match_batch_size: false
    global_shuffle: false
    group_feature_name: ''
    group_id: ''
    io_mode: false
    local_shuffle_window: 0
    nonclk_score_coeff: 1.0
    parse_thread_num: 32
    passwise_shuffle_replay_num: 0
    pipeline_config_path: ./sample_server_config.json
    read_thread_num: 64
    reader_configs: SampleServerConfig
    show_score_coeff: 1.0
    train_pass_size: 10240
    use_nonclk_feature_score: false
load_option:
  cmd_suffix: ''
  cold_start: false
  consumer_num: 8
  dense_format: general_text
  dense_path: viewfs:///home/<USER>/model/dsp_lps_zx_search_mtl_new/2023-08-21-19-23-35
  fix_slot_when_loading_simple_mio_binary_v2: true
  force_load_init_model: false
  init_model_hour: ''
  init_model_index: -1
  init_model_path: viewfs:///home/<USER>/model/dsp_lps_zx_search_mtl_new/2023-08-21-19-23-35
  load_channel_block_size: 20000
  load_channel_buffer_size: 21474836480
  load_channel_capacity: 1280000
  load_dense_by_name: false
  load_from_other_model_path: false
  model_shift_seconds: 0
  producer_num: 8
  resize_dim: false
  slot_filter: blacklist
  slots: []
  sparse_format: GeneralBinary
  sparse_path: viewfs:///home/<USER>/model/dsp_lps_zx_search_mtl_new/2023-08-21-19-23-35
  value_only: false
load_option_trigger: HdfsLoadOption
recycle_option:
  check_shrink_interval_pass: 5
  recycle_interval_hour: 24
  recycle_selected_hours: ''
  slot_filter: blacklist
  slots: []
recycle_option_trigger: UnseenDaysRecycle
runtime_option:
  clear_before_routine_load: false
  dynamic_test_data: false
  enable_tensorboard: true
  force_final_test: true
  mode: train
  routine_load_dense: true
  routine_load_interval_hour: -1
  routine_load_interval_pass: -1
  routine_load_model_hour: ''
  routine_load_model_index: -1
  routine_load_model_path: ''
  routine_load_selected_hours: ''
  routine_load_sparse: true
  tensorboard_log_dir: ''
  tensorboard_save_interval_steps: 1
  test_begin_time_diff_with_train: 0
  test_end_time_diff_with_train: 0
  test_info_print_interval_step: 100
  test_interval_hour: -1
  test_interval_pass: -1
  test_offset_hours: ''
  test_selected_hours: ''
  use_routine_load: false
runtime_option_trigger: GeneralRuntimeOption
save_option:
  allow_failed_time: 0
  async_save: false
  channel_buffer_size: 10737418240
  dense_path: ''
  io_buffer_size: 51200
  keep_checkpoint_size: 7
  keep_model_size: 7
  model_path: ''
  path_prefix: checkpoint
  rollback_model_at_new_day: true
  rollback_model_at_train_end: false
  rollback_model_hours: ''
  rollback_model_interval_hour: -1
  rollback_model_interval_pass: -1
  rollback_model_path: ''
  save_model_at_new_day: true
  save_model_at_train_end: true
  save_model_hours: ''
  save_model_interval_hour: 3
  save_model_interval_pass: -1
  slot_filter: blacklist
  slots: []
  sparse_format: GeneralBinary
  sparse_path: ''
  use_save: true
  use_save_rollback: true
save_option_trigger: HdfsSaveOption
slot_info_option:
  get_slot_info_hours: ''
  get_slot_info_interval_hour: -1
  get_slot_info_interval_pass: -1
  thres: 0.0
slot_info_option_trigger: GetSlotInfoOption
static_flags:
  asynclogtofile: 'True'
  logging_switch_uid_mod_divisor: '19999'
  max_body_size: '2147483647'
  max_log_size: '500'
  perf_log_to_file: 'False'
  stop_logging_if_full_disk: 'True'
  use_kml_env_for_sample_server: 'true'
  use_weighted_auc: 'True'
time_decay_option:
  decay_ratio: 0.9900000095367432
time_decay_option_trigger: TimeDecayOption
user_param: {}
user_static_flags: {}
